#!/bin/bash

# Kubernetes 镜像导入脚本
# 在内网环境中运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

echo "========================================"
echo "    Kubernetes 镜像导入工具"
echo "========================================"
echo

# 检查镜像文件
if [ ! -f "k8s-images.tar" ]; then
    log_error "镜像文件 k8s-images.tar 不存在"
    exit 1
fi

# 检查 Docker
if ! command -v docker >/dev/null 2>&1; then
    log_error "Docker 未安装"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    log_error "Docker 未运行，请启动 Docker 服务"
    exit 1
fi

log_info "开始导入 Kubernetes 镜像..."

# 导入镜像
if docker load -i k8s-images.tar; then
    log_success "镜像导入完成"
else
    log_error "镜像导入失败"
    exit 1
fi

# 显示导入的镜像
log_info "已导入的 Kubernetes 镜像："
docker images | grep -E "(kube-|coredns|etcd|pause|flannel|calico)" | head -20

log_success "所有镜像已成功导入！"
echo
echo "现在可以运行 kubeadm init 初始化集群"
