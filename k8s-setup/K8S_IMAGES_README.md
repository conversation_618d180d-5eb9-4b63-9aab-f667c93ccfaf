# Kubernetes 离线镜像包使用指南

## 文件说明

- `k8s-images.tar` - Kubernetes 镜像包
- `import-k8s-images.sh` - 镜像导入脚本
- `K8S_IMAGES_README.md` - 本说明文件

## 使用步骤

### 1. 传输文件到内网环境
将以下文件传输到内网服务器：
- k8s-images.tar
- import-k8s-images.sh

### 2. 导入镜像
```bash
# 在内网服务器上运行
chmod +x import-k8s-images.sh
./import-k8s-images.sh
```

### 3. 验证镜像
```bash
# 查看导入的镜像
docker images | grep -E "(kube-|coredns|etcd|pause|flannel)"
```

### 4. 初始化集群
```bash
# 使用本地镜像初始化集群
kubeadm init \
  --apiserver-advertise-address=<您的IP> \
  --pod-network-cidr=**********/16 \
  --service-cidr=*********/12 \
  --ignore-preflight-errors=all
```

## 包含的镜像

### Kubernetes 核心组件
- kube-apiserver:v1.33.4
- kube-controller-manager:v1.33.4
- kube-scheduler:v1.33.4
- kube-proxy:v1.33.4
- pause:3.10
- etcd:3.5.15-0
- coredns:v1.11.3

### 网络插件
- Flannel v0.19.2
- Calico v3.26.1 (备用)

### 其他组件
- metrics-server (监控)
- local-volume-provisioner (存储)

## 注意事项

1. 确保目标服务器已安装 Docker
2. 确保有足够的磁盘空间
3. 导入过程可能需要几分钟时间
4. 如果某些镜像导入失败，可以单独处理

## 故障排除

### 导入失败
- 检查 Docker 服务状态
- 检查磁盘空间
- 检查镜像文件完整性

### 镜像缺失
- 检查镜像是否正确导入
- 手动拉取缺失的镜像
- 使用 docker images 查看可用镜像

更多信息请参考 Kubernetes 官方文档。
