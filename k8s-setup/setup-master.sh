#!/bin/bash

# Kubernetes Master 节点初始化脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先运行 setup-common.sh"
        exit 1
    fi
    
    # 检查 kubeadm
    if ! command -v kubeadm &> /dev/null; then
        log_error "kubeadm 未安装，请先运行 setup-common.sh"
        exit 1
    fi
    
    # 检查主机名
    if [[ $(hostname) != "k8s-master" ]]; then
        log_warning "主机名不是 k8s-master，当前主机名: $(hostname)"
    fi
    
    log_success "前置条件检查通过"
}

# 获取网络配置
get_network_config() {
    log_info "配置网络参数..."
    
    # 获取本机 IP
    local_ip=$(ip route get ******* | awk '{print $7; exit}')
    log_info "检测到本机 IP: $local_ip"
    
    read -p "请确认 Master 节点 IP [$local_ip]: " master_ip
    master_ip=${master_ip:-$local_ip}
    
    read -p "请输入 Pod 网络 CIDR [**********/16]: " pod_cidr
    pod_cidr=${pod_cidr:-**********/16}
    
    read -p "请输入 Service 网络 CIDR [*********/12]: " service_cidr
    service_cidr=${service_cidr:-*********/12}
    
    log_info "网络配置："
    echo "  Master IP: $master_ip"
    echo "  Pod CIDR: $pod_cidr"
    echo "  Service CIDR: $service_cidr"
}

# 初始化集群
init_cluster() {
    log_info "初始化 Kubernetes 集群..."
    
    # 初始化集群
    log_info "正在初始化集群，这可能需要几分钟..."
    kubeadm init \
        --apiserver-advertise-address=$master_ip \
        --pod-network-cidr=$pod_cidr \
        --service-cidr=$service_cidr
    
    if [[ $? -eq 0 ]]; then
        log_success "集群初始化完成"
    else
        log_error "集群初始化失败"
        exit 1
    fi
}

# 配置 kubectl
setup_kubectl() {
    log_info "配置 kubectl..."
    
    mkdir -p $HOME/.kube
    cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
    chown $(id -u):$(id -g) $HOME/.kube/config
    
    # 为普通用户也配置 kubectl（如果存在）
    if [[ -n "$SUDO_USER" ]]; then
        local user_home=$(eval echo ~$SUDO_USER)
        mkdir -p $user_home/.kube
        cp -i /etc/kubernetes/admin.conf $user_home/.kube/config
        chown $SUDO_USER:$SUDO_USER $user_home/.kube/config
        log_info "已为用户 $SUDO_USER 配置 kubectl"
    fi
    
    log_success "kubectl 配置完成"
}

# 安装网络插件
install_network_plugin() {
    log_info "安装网络插件 (Flannel)..."
    
    # 下载并应用 Flannel
    kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
    
    if [[ $? -eq 0 ]]; then
        log_success "网络插件安装完成"
    else
        log_warning "网络插件安装可能失败，请手动检查"
    fi
}

# 等待节点就绪
wait_for_node_ready() {
    log_info "等待节点就绪..."
    
    local timeout=300
    local elapsed=0
    
    while [[ $elapsed -lt $timeout ]]; do
        if kubectl get nodes | grep -q "Ready"; then
            log_success "节点已就绪"
            return 0
        fi
        
        echo -n "."
        sleep 10
        elapsed=$((elapsed + 10))
    done
    
    log_warning "节点在 $timeout 秒内未就绪，请手动检查"
}

# 配置存储类
setup_storage_class() {
    log_info "配置默认存储类..."
    
    cat > local-storage-class.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
EOF
    
    kubectl apply -f local-storage-class.yaml
    log_success "存储类配置完成"
}

# 允许 Master 节点调度（可选）
allow_master_scheduling() {
    echo
    read -p "是否允许 Master 节点运行应用 Pod？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "配置 Master 节点允许调度..."
        kubectl taint nodes --all node-role.kubernetes.io/master- 2>/dev/null || true
        kubectl taint nodes --all node-role.kubernetes.io/control-plane- 2>/dev/null || true
        log_success "Master 节点现在可以运行应用 Pod"
    fi
}

# 显示集群信息
show_cluster_info() {
    log_info "集群信息："
    echo
    
    echo "=== 节点状态 ==="
    kubectl get nodes -o wide
    
    echo
    echo "=== 系统 Pod 状态 ==="
    kubectl get pods -n kube-system
    
    echo
    echo "=== 集群信息 ==="
    kubectl cluster-info
    
    echo
    echo "=== Worker 节点加入命令 ==="
    log_warning "请在 Worker 节点上运行以下命令："
    kubeadm token create --print-join-command
}

# 生成 Worker 加入脚本
generate_worker_script() {
    log_info "生成 Worker 节点加入脚本..."
    
    local join_command=$(kubeadm token create --print-join-command)
    
    cat > join-worker.sh << EOF
#!/bin/bash

# Worker 节点加入集群脚本
# 在 Worker 节点上以 root 权限运行

set -e

echo "正在加入 Kubernetes 集群..."
$join_command

if [[ \$? -eq 0 ]]; then
    echo "成功加入集群！"
    echo "请在 Master 节点上运行 'kubectl get nodes' 验证"
else
    echo "加入集群失败，请检查网络连接和防火墙设置"
    exit 1
fi
EOF
    
    chmod +x join-worker.sh
    log_success "Worker 加入脚本已生成: join-worker.sh"
}

# 主函数
main() {
    echo "========================================"
    echo "    Kubernetes Master 节点初始化"
    echo "========================================"
    echo
    
    check_root
    check_prerequisites
    get_network_config
    init_cluster
    setup_kubectl
    install_network_plugin
    wait_for_node_ready
    setup_storage_class
    allow_master_scheduling
    show_cluster_info
    generate_worker_script
    
    echo
    echo "========================================"
    echo "           初始化完成"
    echo "========================================"
    echo
    log_success "Master 节点初始化完成！"
    echo
    echo "下一步："
    echo "1. 将 join-worker.sh 复制到 Worker 节点"
    echo "2. 在 Worker 节点上以 root 权限运行: ./join-worker.sh"
    echo "3. 验证集群状态: kubectl get nodes"
    echo "4. 部署 Dify: cd ../k8s-deployment && ./deploy.sh"
    echo
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
