#!/bin/bash

# Kubernetes 最小镜像包导出脚本
# 只拉取最关键的镜像，适用于网络不稳定的环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

echo "========================================"
echo "    Kubernetes 最小镜像包导出工具"
echo "========================================"
echo

# 检查 Docker
if ! command -v docker >/dev/null 2>&1; then
    log_error "Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    log_error "Docker 未运行，请启动 Docker 服务"
    exit 1
fi

# Kubernetes 版本
K8S_VERSION="1.33.4"
ETCD_VERSION="3.5.15-0"
COREDNS_VERSION="v1.11.3"
PAUSE_VERSION="3.10"

log_info "准备导出 Kubernetes v$K8S_VERSION 最小镜像包"

# 最小镜像列表（只包含绝对必需的）
MINIMAL_IMAGES=(
    "registry.aliyuncs.com/google_containers/kube-apiserver:v$K8S_VERSION"
    "registry.aliyuncs.com/google_containers/kube-controller-manager:v$K8S_VERSION"
    "registry.aliyuncs.com/google_containers/kube-scheduler:v$K8S_VERSION"
    "registry.aliyuncs.com/google_containers/kube-proxy:v$K8S_VERSION"
    "registry.aliyuncs.com/google_containers/pause:$PAUSE_VERSION"
    "registry.aliyuncs.com/google_containers/etcd:$ETCD_VERSION"
    "registry.aliyuncs.com/google_containers/coredns:$COREDNS_VERSION"
)

# 拉取最小镜像集
pull_minimal_images() {
    log_info "开始拉取最小镜像集..."
    local success_count=0
    local total_count=${#MINIMAL_IMAGES[@]}
    
    for image in "${MINIMAL_IMAGES[@]}"; do
        log_info "拉取镜像: $image"
        if timeout 120 docker pull "$image"; then
            log_success "✓ $image"
            success_count=$((success_count + 1))
        else
            log_error "✗ $image"
        fi
    done
    
    log_info "最小镜像拉取完成: $success_count/$total_count"
    
    if [ $success_count -lt 5 ]; then
        log_error "关键镜像拉取失败过多，无法创建可用的镜像包"
        exit 1
    fi
    
    if [ $success_count -eq $total_count ]; then
        log_success "所有最小镜像拉取成功！"
    else
        log_warning "部分镜像拉取失败，但足够创建基本的集群"
    fi
}

# 导出镜像
export_minimal_images() {
    log_info "导出最小镜像包到 k8s-minimal-images.tar..."
    
    # 获取实际存在的镜像
    local existing_images=()
    for image in "${MINIMAL_IMAGES[@]}"; do
        if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${image}$"; then
            existing_images+=("$image")
        fi
    done
    
    if [ ${#existing_images[@]} -eq 0 ]; then
        log_error "没有找到可导出的镜像"
        exit 1
    fi
    
    log_info "导出 ${#existing_images[@]} 个镜像..."
    
    if docker save "${existing_images[@]}" -o k8s-minimal-images.tar; then
        local file_size=$(du -h k8s-minimal-images.tar | cut -f1)
        log_success "最小镜像包导出完成: k8s-minimal-images.tar ($file_size)"
    else
        log_error "镜像导出失败"
        exit 1
    fi
}

# 生成导入脚本
generate_minimal_import_script() {
    log_info "生成最小镜像包导入脚本..."
    
    cat > import-minimal-k8s-images.sh << 'EOF'
#!/bin/bash

# Kubernetes 最小镜像包导入脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

log_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

echo "========================================"
echo "    Kubernetes 最小镜像包导入"
echo "========================================"
echo

# 检查镜像文件
if [ ! -f "k8s-minimal-images.tar" ]; then
    log_error "镜像文件 k8s-minimal-images.tar 不存在"
    exit 1
fi

# 检查 Docker
if ! docker info >/dev/null 2>&1; then
    log_error "Docker 未运行，请启动 Docker 服务"
    exit 1
fi

log_info "开始导入最小 Kubernetes 镜像..."

# 显示文件信息
file_size=$(du -h k8s-minimal-images.tar | cut -f1)
log_info "镜像文件大小: $file_size"

# 导入镜像
if docker load -i k8s-minimal-images.tar; then
    log_success "最小镜像包导入完成"
else
    log_error "镜像导入失败"
    exit 1
fi

# 显示导入的镜像
echo
log_info "已导入的 Kubernetes 核心镜像："
docker images | grep -E "(kube-|coredns|etcd|pause)" | head -10

echo
log_success "最小镜像包导入完成！"
echo
echo "注意："
echo "- 此镜像包只包含 Kubernetes 核心组件"
echo "- 网络插件需要单独安装或配置"
echo "- 建议使用 host-local 网络或手动配置网络"
echo
echo "下一步："
echo "kubeadm init \\"
echo "  --apiserver-advertise-address=<您的IP> \\"
echo "  --pod-network-cidr=**********/16 \\"
echo "  --ignore-preflight-errors=all"
EOF

    chmod +x import-minimal-k8s-images.sh
    log_success "最小镜像包导入脚本已生成: import-minimal-k8s-images.sh"
}

# 生成使用说明
generate_minimal_readme() {
    log_info "生成最小镜像包使用说明..."
    
    cat > MINIMAL_IMAGES_README.md << EOF
# Kubernetes 最小镜像包使用指南

## 文件说明

- \`k8s-minimal-images.tar\` - Kubernetes 最小镜像包
- \`import-minimal-k8s-images.sh\` - 镜像导入脚本
- \`MINIMAL_IMAGES_README.md\` - 本说明文件

## 包含的镜像

### Kubernetes 核心组件 (v$K8S_VERSION)
- kube-apiserver:v$K8S_VERSION
- kube-controller-manager:v$K8S_VERSION
- kube-scheduler:v$K8S_VERSION
- kube-proxy:v$K8S_VERSION
- pause:$PAUSE_VERSION
- etcd:$ETCD_VERSION
- coredns:$COREDNS_VERSION

## 使用步骤

### 1. 传输文件到内网环境
将以下文件传输到内网服务器：
- k8s-minimal-images.tar
- import-minimal-k8s-images.sh

### 2. 导入镜像
\`\`\`bash
chmod +x import-minimal-k8s-images.sh
./import-minimal-k8s-images.sh
\`\`\`

### 3. 初始化集群
\`\`\`bash
kubeadm init \\
  --apiserver-advertise-address=<您的IP> \\
  --pod-network-cidr=**********/16 \\
  --service-cidr=*********/12 \\
  --ignore-preflight-errors=all
\`\`\`

### 4. 配置网络（重要）
由于最小镜像包不包含网络插件，需要手动配置：

#### 方案1：使用简单的 host-local 网络
\`\`\`bash
# 创建简单的网络配置
mkdir -p /etc/cni/net.d
cat > /etc/cni/net.d/10-bridge.conf << 'NETEOF'
{
    "cniVersion": "0.3.1",
    "name": "bridge",
    "type": "bridge",
    "bridge": "cnio0",
    "isGateway": true,
    "ipMasq": true,
    "ipam": {
        "type": "host-local",
        "ranges": [
            [{"subnet": "**********/16"}]
        ],
        "routes": [{"dst": "0.0.0.0/0"}]
    }
}
NETEOF
\`\`\`

#### 方案2：手动安装 Flannel
\`\`\`bash
# 如果网络允许，可以尝试安装 Flannel
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
\`\`\`

## 注意事项

1. **网络配置**：最小镜像包不包含网络插件，需要手动配置
2. **存储配置**：需要手动配置存储类
3. **监控组件**：不包含 metrics-server 等监控组件
4. **功能限制**：只能运行基本的 Kubernetes 功能

## 优势

- **文件小**：镜像包大小显著减少
- **拉取快**：只拉取必需镜像，速度更快
- **网络友好**：适合网络不稳定的环境
- **核心功能**：包含 Kubernetes 所有核心功能

这个最小镜像包适合快速搭建测试环境或网络受限的生产环境。
EOF

    log_success "最小镜像包使用说明已生成: MINIMAL_IMAGES_README.md"
}

# 主函数
main() {
    pull_minimal_images
    export_minimal_images
    generate_minimal_import_script
    generate_minimal_readme
    
    echo
    echo "========================================"
    echo "           最小镜像包导出完成"
    echo "========================================"
    echo
    log_success "Kubernetes 最小镜像包已准备完成！"
    echo
    echo "生成的文件："
    echo "  - k8s-minimal-images.tar (最小镜像包)"
    echo "  - import-minimal-k8s-images.sh (导入脚本)"
    echo "  - MINIMAL_IMAGES_README.md (使用说明)"
    echo
    echo "文件大小更小，传输更快，适合网络受限环境！"
}

# 如果直接运行脚本
if [ "${0##*/}" = "export-minimal-k8s-images.sh" ]; then
    main "$@"
fi
