# Kubernetes 集群快速搭建指南

## 🚀 通用脚本搭建（推荐，兼容性最好）

### 步骤 1: 在 Master 节点运行
```bash
# 使用兼容性最好的通用脚本
bash universal-setup.sh
```
选择 "1) Master 节点"，按提示配置：
- 确认 Master IP 地址
- 选择单节点或多节点集群

### 步骤 2: 在 Worker 节点运行（多节点集群）
```bash
# 方法1: 使用通用脚本
bash universal-setup.sh
# 选择 "2) Worker 节点"，输入 Master IP 和节点编号

# 方法2: 使用 Master 生成的脚本
bash worker-setup.sh
# 输入 Worker 节点编号即可
```

## 🔧 原版脚本（如果通用脚本有问题）

### 步骤 1: 在 Master 节点运行
```bash
bash quick-setup.sh
```
选择 "1) Master 节点"，按提示输入网络配置。

### 步骤 2: 在 Worker 节点运行
```bash
bash quick-setup.sh
```
选择 "2) Worker 节点"，然后运行 Master 节点生成的 join 命令。

### 步骤 3: 验证集群
在 Master 节点上运行：
```bash
kubectl get nodes
```

## 🔧 分步搭建（高级用户）

### 步骤 1: 所有节点基础配置
在每个节点上运行：
```bash
chmod +x setup-common.sh
./setup-common.sh
```

### 步骤 2: 初始化 Master 节点
在 Master 节点上运行：
```bash
chmod +x setup-master.sh
./setup-master.sh
```

### 步骤 3: Worker 节点加入集群
将 Master 节点生成的 `join-worker.sh` 复制到 Worker 节点并运行：
```bash
chmod +x join-worker.sh
./join-worker.sh
```

## ✅ 验证集群状态

```bash
# 查看节点状态
kubectl get nodes

# 查看系统 Pod
kubectl get pods -n kube-system

# 查看集群信息
kubectl cluster-info
```

## 🎯 部署 Dify

集群搭建完成后，部署 Dify：
```bash
cd ../k8s-deployment
./deploy.sh  # Linux
# 或
deploy.bat   # Windows
```

## 🚨 常见问题

### 1. GPG 密钥下载失败
```bash
# 错误：curl: (35) Recv failure: Connection reset by peer
# 解决方案1：使用离线脚本
bash offline-setup.sh

# 解决方案2：跳过 GPG 验证
# Ubuntu:
cat > /etc/apt/sources.list.d/kubernetes.list << EOF
deb [trusted=yes] https://apt.kubernetes.io/ kubernetes-xenial main
EOF
apt-get update
apt-get install -y kubelet kubeadm kubectl --allow-unauthenticated

# CentOS:
cat > /etc/yum.repos.d/kubernetes.repo << EOF
[kubernetes]
name=Kubernetes
baseurl=https://packages.cloud.google.com/yum/repos/kubernetes-el7-x86_64
enabled=1
gpgcheck=0
repo_gpgcheck=0
EOF
yum install -y kubelet kubeadm kubectl
```

### 2. shell 兼容性问题
```bash
# 错误：Bad substitution
# 解决方案：使用 bash 运行
bash universal-setup.sh  # 推荐
# 或
bash offline-setup.sh    # 内网环境
```

### 3. 节点 NotReady
```bash
# 检查网络插件
kubectl get pods -n kube-flannel

# 重启 kubelet
systemctl restart kubelet
```

### 4. join 命令过期
在 Master 节点重新生成：
```bash
kubeadm token create --print-join-command
```

### 5. 网络问题
检查防火墙和 SELinux：
```bash
systemctl status firewalld
getenforce
```

## 📋 服务器要求

### 支持的集群规模
- **单节点集群**：1台服务器（Master 兼 Worker）
- **多节点集群**：1台 Master + N台 Worker（N ≥ 1）

### 硬件要求
- **最低配置**：每台4GB内存，2CPU核心，50GB磁盘
- **推荐配置**：每台8GB内存，4CPU核心，100GB磁盘

### 软件要求
- **操作系统**：CentOS 7.9+ / RHEL 7+ / Ubuntu 18.04+ / Debian 10+
- **网络**：内网互通
- **权限**：root 用户权限

## 🔗 相关文档

- [详细搭建指南](README.md)
- [手动安装指南](MANUAL_INSTALL.md) - 解决网络问题
- [Dify 部署指南](../k8s-deployment/README.md)

## 📋 脚本选择指南

| 脚本 | 适用场景 | 特点 |
|------|----------|------|
| `universal-setup.sh` | 有网络连接 | 兼容性最好，推荐使用 |
| `offline-setup.sh` | 内网环境 | 跳过网络验证，离线安装 |
| `quick-setup.sh` | 测试环境 | 原版脚本，可能有兼容性问题 |
| 手动安装 | 脚本失败时 | 完全手动控制，最可靠 |
