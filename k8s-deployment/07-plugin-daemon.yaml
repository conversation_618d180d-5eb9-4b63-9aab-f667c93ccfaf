apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: plugin-daemon-pvc
  namespace: dify
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-plugin-daemon
  namespace: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dify-plugin-daemon
  template:
    metadata:
      labels:
        app: dify-plugin-daemon
    spec:
      containers:
      - name: plugin-daemon
        image: langgenius/dify-plugin-daemon:0.2.0-local
        env:
        - name: DB_USERNAME
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_PORT
        - name: DB_DATABASE
          value: "dify_plugin"
        - name: SERVER_PORT
          value: "5002"
        - name: SERVER_KEY
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: PLUGIN_DAEMON_KEY
        - name: MAX_PLUGIN_PACKAGE_SIZE
          value: "52428800"
        - name: PPROF_ENABLED
          value: "false"
        - name: DIFY_INNER_API_URL
          value: "http://dify-api:5001"
        - name: DIFY_INNER_API_KEY
          value: "QaHbTe77CtuXmsfyhR7+vRjI/+XbV1AaFy691iy+kGDv2Jvy0/eAh8Y1"
        - name: PLUGIN_WORKING_PATH
          value: "/app/storage/cwd"
        - name: FORCE_VERIFYING_SIGNATURE
          value: "true"
        - name: PLUGIN_STORAGE_TYPE
          value: "local"
        - name: PLUGIN_STORAGE_LOCAL_ROOT
          value: "/app/storage"
        ports:
        - containerPort: 5002
        - containerPort: 5003
        volumeMounts:
        - name: plugin-storage
          mountPath: /app/storage
      volumes:
      - name: plugin-storage
        persistentVolumeClaim:
          claimName: plugin-daemon-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: dify-plugin-daemon
  namespace: dify
spec:
  selector:
    app: dify-plugin-daemon
  ports:
  - name: daemon
    port: 5002
    targetPort: 5002
  - name: debug
    port: 5003
    targetPort: 5003
