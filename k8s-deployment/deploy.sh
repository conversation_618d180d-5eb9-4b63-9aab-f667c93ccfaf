#!/bin/bash

# Dify Kubernetes 部署脚本 - 内网环境版本
# 适用于无外网连接的 Kubernetes 集群

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 kubectl 是否可用
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未找到，请先安装 kubectl"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群，请检查 kubeconfig"
        exit 1
    fi
    
    log_success "Kubernetes 集群连接正常"
}

# 检查镜像是否存在
check_images() {
    log_info "检查必需的 Docker 镜像..."
    
    local images=(
        "langgenius/dify-api:1.8.0"
        "langgenius/dify-web:1.8.0"
        "langgenius/dify-sandbox:0.2.12"
        "langgenius/dify-plugin-daemon:0.2.0-local"
        "postgres:15-alpine"
        "redis:6-alpine"
        "nginx:latest"
        "semitechnologies/weaviate:1.19.0"
        "ubuntu/squid:latest"
    )
    
    local missing_images=()
    
    for image in "${images[@]}"; do
        # 检查镜像是否在集群节点上存在
        if ! kubectl get nodes -o jsonpath='{.items[*].status.images[*].names[*]}' | grep -q "$image"; then
            missing_images+=("$image")
        fi
    done
    
    if [ ${#missing_images[@]} -gt 0 ]; then
        log_warning "以下镜像在集群中未找到："
        for image in "${missing_images[@]}"; do
            echo "  - $image"
        done
        log_warning "请确保这些镜像已经导入到所有 Kubernetes 节点"
        read -p "是否继续部署？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_success "所有必需镜像已准备就绪"
    fi
}

# 创建存储类（如果需要）
create_storage_class() {
    log_info "检查存储类..."
    
    if ! kubectl get storageclass default &> /dev/null; then
        log_warning "未找到默认存储类，创建本地存储类..."
        
        cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
EOF
        
        log_success "本地存储类创建完成"
    else
        log_success "存储类已存在"
    fi
}

# 部署 Dify
deploy_dify() {
    log_info "开始部署 Dify..."
    
    # 按顺序部署各个组件
    local yaml_files=(
        "00-namespace.yaml"
        "01-configmap.yaml"
        "02-postgres.yaml"
        "03-redis.yaml"
        "04-weaviate.yaml"
        "05-ssrf-proxy.yaml"
        "06-sandbox.yaml"
        "07-plugin-daemon.yaml"
        "08-api.yaml"
        "09-worker.yaml"
        "10-web.yaml"
        "11-nginx.yaml"
    )
    
    for yaml_file in "${yaml_files[@]}"; do
        if [ -f "$yaml_file" ]; then
            log_info "部署 $yaml_file..."
            kubectl apply -f "$yaml_file"
            
            # 等待关键服务启动
            case "$yaml_file" in
                "02-postgres.yaml")
                    log_info "等待 PostgreSQL 启动..."
                    kubectl wait --for=condition=ready pod -l app=dify-postgres -n dify --timeout=300s
                    ;;
                "03-redis.yaml")
                    log_info "等待 Redis 启动..."
                    kubectl wait --for=condition=ready pod -l app=dify-redis -n dify --timeout=300s
                    ;;
                "04-weaviate.yaml")
                    log_info "等待 Weaviate 启动..."
                    kubectl wait --for=condition=ready pod -l app=dify-weaviate -n dify --timeout=300s
                    ;;
            esac
        else
            log_error "文件 $yaml_file 不存在"
            exit 1
        fi
    done
    
    log_success "Dify 部署完成"
}

# 检查部署状态
check_deployment() {
    log_info "检查部署状态..."
    
    echo
    echo "=== Namespace ==="
    kubectl get namespace dify
    
    echo
    echo "=== Pods ==="
    kubectl get pods -n dify -o wide
    
    echo
    echo "=== Services ==="
    kubectl get services -n dify
    
    echo
    echo "=== PersistentVolumeClaims ==="
    kubectl get pvc -n dify
    
    # 检查是否所有 Pod 都在运行
    local not_ready_pods=$(kubectl get pods -n dify --no-headers | grep -v Running | grep -v Completed | wc -l)
    
    if [ "$not_ready_pods" -eq 0 ]; then
        log_success "所有 Pod 都在正常运行"
        
        # 获取访问地址
        local nginx_service=$(kubectl get service dify-nginx -n dify -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [ -z "$nginx_service" ]; then
            nginx_service=$(kubectl get service dify-nginx -n dify -o jsonpath='{.spec.clusterIP}')
        fi
        
        echo
        log_success "Dify 部署成功！"
        echo "访问地址: http://$nginx_service"
        echo
        echo "如果使用 LoadBalancer 类型的服务，请等待外部 IP 分配完成"
        echo "可以使用以下命令查看服务状态："
        echo "  kubectl get service dify-nginx -n dify -w"
        
    else
        log_warning "有 $not_ready_pods 个 Pod 未就绪，请检查日志"
        echo "使用以下命令查看 Pod 日志："
        echo "  kubectl logs -n dify <pod-name>"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "    Dify Kubernetes 内网部署脚本"
    echo "========================================"
    echo
    
    check_kubectl
    check_images
    create_storage_class
    deploy_dify
    
    echo
    log_info "等待所有服务启动..."
    sleep 30
    
    check_deployment
    
    echo
    echo "========================================"
    echo "           部署完成"
    echo "========================================"
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
