#!/bin/bash

# Dify 镜像导出脚本
# 在有网络的环境中运行此脚本来准备所有必需的镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Dify 核心镜像列表
IMAGES=(
    "langgenius/dify-api:1.8.0"
    "langgenius/dify-web:1.8.0"
    "langgenius/dify-sandbox:0.2.12"
    "langgenius/dify-plugin-daemon:0.2.0-local"
    "postgres:15-alpine"
    "redis:6-alpine"
    "nginx:latest"
    "semitechnologies/weaviate:1.19.0"
    "ubuntu/squid:latest"
)

# 检查 Docker 是否可用
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未找到，请先安装 Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "无法连接到 Docker daemon，请检查 Docker 服务"
        exit 1
    fi
    
    log_success "Docker 连接正常"
}

# 拉取镜像
pull_images() {
    log_info "开始拉取 Dify 相关镜像..."
    echo
    
    local failed_images=()
    
    for image in "${IMAGES[@]}"; do
        log_info "拉取镜像: $image"
        
        if docker pull "$image"; then
            log_success "成功拉取: $image"
        else
            log_error "拉取失败: $image"
            failed_images+=("$image")
        fi
        echo
    done
    
    if [ ${#failed_images[@]} -gt 0 ]; then
        log_error "以下镜像拉取失败："
        for image in "${failed_images[@]}"; do
            echo "  - $image"
        done
        exit 1
    fi
    
    log_success "所有镜像拉取完成"
}

# 导出镜像
export_images() {
    log_info "导出镜像到 dify-images.tar..."
    
    if docker save "${IMAGES[@]}" -o dify-images.tar; then
        log_success "镜像导出完成: dify-images.tar"
        
        # 显示文件大小
        local file_size=$(du -h dify-images.tar | cut -f1)
        log_info "文件大小: $file_size"
    else
        log_error "镜像导出失败"
        exit 1
    fi
}

# 验证导出的镜像
verify_export() {
    log_info "验证导出的镜像..."
    
    if [ ! -f "dify-images.tar" ]; then
        log_error "导出文件不存在"
        exit 1
    fi
    
    # 检查文件是否为空
    if [ ! -s "dify-images.tar" ]; then
        log_error "导出文件为空"
        exit 1
    fi
    
    log_success "镜像文件验证通过"
}

# 生成导入脚本
generate_import_script() {
    log_info "生成镜像导入脚本..."
    
    cat > import-images.sh << 'EOF'
#!/bin/bash

# Dify 镜像导入脚本
# 在内网环境中运行此脚本来导入镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查镜像文件
if [ ! -f "dify-images.tar" ]; then
    log_error "镜像文件 dify-images.tar 不存在"
    exit 1
fi

log_info "开始导入 Dify 镜像..."

# 导入镜像
if docker load -i dify-images.tar; then
    log_success "镜像导入完成"
else
    log_error "镜像导入失败"
    exit 1
fi

# 显示导入的镜像
log_info "已导入的镜像："
docker images | grep -E "(langgenius|postgres|redis|nginx|weaviate|ubuntu)" | head -20

log_success "所有镜像已成功导入"
EOF

    chmod +x import-images.sh
    log_success "导入脚本已生成: import-images.sh"
}

# 生成 Windows 导入脚本
generate_windows_import_script() {
    log_info "生成 Windows 镜像导入脚本..."
    
    cat > import-images.bat << 'EOF'
@echo off
REM Dify 镜像导入脚本 - Windows 版本

echo [INFO] 检查镜像文件...
if not exist "dify-images.tar" (
    echo [ERROR] 镜像文件 dify-images.tar 不存在
    pause
    exit /b 1
)

echo [INFO] 开始导入 Dify 镜像...
docker load -i dify-images.tar

if errorlevel 1 (
    echo [ERROR] 镜像导入失败
    pause
    exit /b 1
)

echo [SUCCESS] 镜像导入完成

echo [INFO] 已导入的镜像：
docker images | findstr /R "langgenius postgres redis nginx weaviate ubuntu"

echo [SUCCESS] 所有镜像已成功导入
pause
EOF

    log_success "Windows 导入脚本已生成: import-images.bat"
}

# 生成使用说明
generate_instructions() {
    log_info "生成使用说明..."
    
    cat > EXPORT_README.md << 'EOF'
# Dify 镜像导出包使用说明

## 文件说明

- `dify-images.tar` - Dify 所有必需的 Docker 镜像
- `import-images.sh` - Linux/macOS 镜像导入脚本
- `import-images.bat` - Windows 镜像导入脚本
- `EXPORT_README.md` - 本说明文件

## 使用步骤

### 1. 传输文件
将以下文件传输到内网环境：
- dify-images.tar
- import-images.sh (Linux/macOS) 或 import-images.bat (Windows)

### 2. 导入镜像

#### Linux/macOS:
```bash
chmod +x import-images.sh
./import-images.sh
```

#### Windows:
```cmd
import-images.bat
```

### 3. 验证导入
```bash
# 查看导入的镜像
docker images | grep -E "(langgenius|postgres|redis|nginx|weaviate|ubuntu)"
```

### 4. 分发到集群节点
如果使用 Kubernetes 集群，需要将镜像分发到所有节点：

#### 方法1: 重新导入
在每个节点上重复步骤2

#### 方法2: 使用私有镜像仓库
1. 搭建私有镜像仓库
2. 推送镜像到私有仓库
3. 修改 Kubernetes 部署文件中的镜像地址

## 镜像列表

本包包含以下镜像：
- langgenius/dify-api:1.8.0
- langgenius/dify-web:1.8.0
- langgenius/dify-sandbox:0.2.12
- langgenius/dify-plugin-daemon:0.2.0-local
- postgres:15-alpine
- redis:6-alpine
- nginx:latest
- semitechnologies/weaviate:1.19.0
- ubuntu/squid:latest

## 注意事项

1. 确保目标环境已安装 Docker
2. 确保有足够的磁盘空间
3. 导入过程可能需要一些时间，请耐心等待
4. 如果导入失败，请检查 Docker 服务状态

## 故障排除

### 导入失败
- 检查 Docker 服务是否运行
- 检查磁盘空间是否充足
- 检查镜像文件是否完整

### 权限问题
- 确保当前用户有 Docker 操作权限
- Linux 下可能需要使用 sudo

更多信息请参考 Dify 官方文档。
EOF

    log_success "使用说明已生成: EXPORT_README.md"
}

# 主函数
main() {
    echo "========================================"
    echo "    Dify 镜像导出脚本"
    echo "========================================"
    echo
    
    check_docker
    pull_images
    export_images
    verify_export
    generate_import_script
    generate_windows_import_script
    generate_instructions
    
    echo
    echo "========================================"
    echo "           导出完成"
    echo "========================================"
    echo
    log_success "镜像导出包已准备完成！"
    echo
    echo "生成的文件："
    echo "  - dify-images.tar (镜像文件)"
    echo "  - import-images.sh (Linux/macOS 导入脚本)"
    echo "  - import-images.bat (Windows 导入脚本)"
    echo "  - EXPORT_README.md (使用说明)"
    echo
    echo "请将这些文件传输到内网环境并按照说明进行导入。"
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
