@echo off
REM Dify Kubernetes 卸载脚本 - Windows 版本

setlocal enabledelayedexpansion

echo ========================================
echo     Dify Kubernetes 卸载脚本
echo ========================================
echo.
echo [WARNING] 此操作将删除 Dify 的所有组件和数据！
echo.

set /p confirm="确定要卸载 Dify 吗？(输入 'yes' 确认): "
if not "!confirm!"=="yes" (
    echo [INFO] 取消卸载
    pause
    exit /b 0
)

echo.
echo [INFO] 开始卸载 Dify...

REM 检查 namespace 是否存在
kubectl get namespace dify >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Dify namespace 不存在，可能已经被删除
    goto :cleanup
)

REM 删除所有部署
echo [INFO] 删除 Deployments...
kubectl delete deployments --all -n dify --ignore-not-found=true

REM 删除所有服务
echo [INFO] 删除 Services...
kubectl delete services --all -n dify --ignore-not-found=true

REM 删除所有 ConfigMaps
echo [INFO] 删除 ConfigMaps...
kubectl delete configmaps --all -n dify --ignore-not-found=true

REM 删除所有 PVC（这将删除所有数据）
echo [WARNING] 删除 PersistentVolumeClaims（这将删除所有数据）...
kubectl delete pvc --all -n dify --ignore-not-found=true

REM 等待资源清理
echo [INFO] 等待资源清理...
timeout /t 10 /nobreak >nul

REM 删除 namespace
echo [INFO] 删除 Namespace...
kubectl delete namespace dify --ignore-not-found=true

REM 等待 namespace 完全删除
echo [INFO] 等待 Namespace 完全删除...
:wait_namespace
kubectl get namespace dify >nul 2>&1
if not errorlevel 1 (
    echo 等待中...
    timeout /t 2 /nobreak >nul
    goto :wait_namespace
)

:cleanup
echo [SUCCESS] Dify 卸载完成

REM 检查残留的 PV
echo [INFO] 检查残留资源...
for /f "tokens=*" %%i in ('kubectl get pv -o jsonpath^="{.items[?(@.spec.claimRef.namespace==\^"dify\^")].metadata.name}" 2^>nul') do set dify_pvs=%%i

if not "!dify_pvs!"=="" (
    echo [WARNING] 发现残留的 PersistentVolumes: !dify_pvs!
    echo.
    set /p delete_pv="是否删除这些 PV？(y/N): "
    if /i "!delete_pv!"=="y" (
        for %%p in (!dify_pvs!) do (
            echo [INFO] 删除 PV: %%p
            kubectl delete pv "%%p" --ignore-not-found=true
        )
    )
)

echo [SUCCESS] 资源清理完成

echo.
echo ========================================
echo            卸载完成
echo ========================================
echo.
echo [SUCCESS] Dify 已完全卸载
echo [INFO] 如果需要重新部署，请运行 deploy.bat
echo.

pause
