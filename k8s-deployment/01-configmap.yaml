apiVersion: v1
kind: ConfigMap
metadata:
  name: dify-config
  namespace: dify
data:
  # 数据库配置
  DB_USERNAME: "postgres"
  DB_PASSWORD: "difyai123456"
  DB_HOST: "dify-postgres"
  DB_PORT: "5432"
  DB_DATABASE: "dify"
  
  # Redis 配置
  REDIS_HOST: "dify-redis"
  REDIS_PORT: "6379"
  REDIS_PASSWORD: "difyai123456"
  REDIS_DB: "0"
  
  # Celery 配置
  CELERY_BROKER_URL: "redis://:difyai123456@dify-redis:6379/1"
  
  # 向量数据库配置
  VECTOR_STORE: "weaviate"
  WEAVIATE_ENDPOINT: "http://dify-weaviate:8080"
  WEAVIATE_API_KEY: "WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih"
  
  # 应用配置
  SECRET_KEY: "************************************************"
  DEPLOY_ENV: "PRODUCTION"
  LOG_LEVEL: "INFO"
  DEBUG: "false"
  FLASK_DEBUG: "false"
  
  # 存储配置
  STORAGE_TYPE: "opendal"
  OPENDAL_SCHEME: "fs"
  OPENDAL_FS_ROOT: "storage"
  
  # 代码执行配置
  CODE_EXECUTION_ENDPOINT: "http://dify-sandbox:8194"
  CODE_EXECUTION_API_KEY: "dify-sandbox"
  
  # 插件配置
  PLUGIN_DAEMON_URL: "http://dify-plugin-daemon:5002"
  PLUGIN_DAEMON_KEY: "lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi"
  
  # 内网环境配置 - 禁用外网检查
  CHECK_UPDATE_URL: ""
  MARKETPLACE_ENABLED: "false"
  MARKETPLACE_API_URL: ""
  
  # SSRF 代理配置
  SSRF_PROXY_HTTP_URL: "http://dify-ssrf-proxy:3128"
  SSRF_PROXY_HTTPS_URL: "http://dify-ssrf-proxy:3128"
