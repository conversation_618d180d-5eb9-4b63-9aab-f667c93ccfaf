apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-worker
  namespace: dify
spec:
  replicas: 2
  selector:
    matchLabels:
      app: dify-worker
  template:
    metadata:
      labels:
        app: dify-worker
    spec:
      containers:
      - name: worker
        image: langgenius/dify-api:1.8.0
        env:
        - name: MODE
          value: "worker"
        - name: DB_USERNAME
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_PORT
        - name: DB_DATABASE
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_DATABASE
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_PORT
        - name: REDIS_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_PASSWORD
        - name: REDIS_DB
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_DB
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: CELERY_BROKER_URL
        - name: SECRET_KEY
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: SECRET_KEY
        - name: DEPLOY_ENV
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DEPLOY_ENV
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: LOG_LEVEL
        - name: DEBUG
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DEBUG
        - name: FLASK_DEBUG
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: FLASK_DEBUG
        - name: VECTOR_STORE
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: VECTOR_STORE
        - name: WEAVIATE_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: WEAVIATE_ENDPOINT
        - name: WEAVIATE_API_KEY
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: WEAVIATE_API_KEY
        - name: STORAGE_TYPE
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: STORAGE_TYPE
        - name: OPENDAL_SCHEME
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: OPENDAL_SCHEME
        - name: OPENDAL_FS_ROOT
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: OPENDAL_FS_ROOT
        - name: CODE_EXECUTION_ENDPOINT
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: CODE_EXECUTION_ENDPOINT
        - name: CODE_EXECUTION_API_KEY
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: CODE_EXECUTION_API_KEY
        - name: PLUGIN_DAEMON_URL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: PLUGIN_DAEMON_URL
        - name: PLUGIN_DAEMON_KEY
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: PLUGIN_DAEMON_KEY
        - name: CHECK_UPDATE_URL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: CHECK_UPDATE_URL
        - name: MARKETPLACE_ENABLED
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: MARKETPLACE_ENABLED
        - name: MARKETPLACE_API_URL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: MARKETPLACE_API_URL
        - name: SSRF_PROXY_HTTP_URL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: SSRF_PROXY_HTTP_URL
        - name: SSRF_PROXY_HTTPS_URL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: SSRF_PROXY_HTTPS_URL
        volumeMounts:
        - name: dify-storage
          mountPath: /app/api/storage
      volumes:
      - name: dify-storage
        persistentVolumeClaim:
          claimName: dify-storage-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-worker-beat
  namespace: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dify-worker-beat
  template:
    metadata:
      labels:
        app: dify-worker-beat
    spec:
      containers:
      - name: worker-beat
        image: langgenius/dify-api:1.8.0
        env:
        - name: MODE
          value: "beat"
        - name: DB_USERNAME
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_PORT
        - name: DB_DATABASE
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DB_DATABASE
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_PORT
        - name: REDIS_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_PASSWORD
        - name: REDIS_DB
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_DB
        - name: CELERY_BROKER_URL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: CELERY_BROKER_URL
        - name: SECRET_KEY
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: SECRET_KEY
        - name: DEPLOY_ENV
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: DEPLOY_ENV
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: LOG_LEVEL
