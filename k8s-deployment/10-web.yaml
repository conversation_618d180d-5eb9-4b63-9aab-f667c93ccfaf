apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-web
  namespace: dify
spec:
  replicas: 2
  selector:
    matchLabels:
      app: dify-web
  template:
    metadata:
      labels:
        app: dify-web
    spec:
      containers:
      - name: web
        image: langgenius/dify-web:1.8.0
        env:
        - name: CONSOLE_API_URL
          value: ""
        - name: APP_API_URL
          value: ""
        - name: NEXT_TELEMETRY_DISABLED
          value: "1"
        - name: TEXT_GENERATION_TIMEOUT_MS
          value: "60000"
        - name: ALLOW_EMBED
          value: "false"
        - name: ALLOW_UNSAFE_DATA_SCHEME
          value: "false"
        - name: MARKETPLACE_API_URL
          value: ""
        - name: MARKETPLACE_URL
          value: ""
        - name: PM2_INSTANCES
          value: "2"
        ports:
        - containerPort: 3000
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: dify-web
  namespace: dify
spec:
  selector:
    app: dify-web
  ports:
  - port: 3000
    targetPort: 3000
