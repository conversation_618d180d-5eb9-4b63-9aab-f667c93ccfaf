apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: sandbox-pvc
  namespace: dify
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-sandbox
  namespace: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dify-sandbox
  template:
    metadata:
      labels:
        app: dify-sandbox
    spec:
      containers:
      - name: sandbox
        image: langgenius/dify-sandbox:0.2.12
        env:
        - name: API_KEY
          value: "dify-sandbox"
        - name: GIN_MODE
          value: "release"
        - name: WORKER_TIMEOUT
          value: "15"
        - name: ENABLE_NETWORK
          value: "true"
        - name: HTTP_PROXY
          value: "http://dify-ssrf-proxy:3128"
        - name: HTTPS_PROXY
          value: "http://dify-ssrf-proxy:3128"
        - name: SANDBOX_PORT
          value: "8194"
        ports:
        - containerPort: 8194
        volumeMounts:
        - name: sandbox-storage
          mountPath: /dependencies
          subPath: dependencies
        - name: sandbox-storage
          mountPath: /conf
          subPath: conf
        livenessProbe:
          httpGet:
            path: /health
            port: 8194
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8194
          initialDelaySeconds: 10
          periodSeconds: 5
      volumes:
      - name: sandbox-storage
        persistentVolumeClaim:
          claimName: sandbox-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: dify-sandbox
  namespace: dify
spec:
  selector:
    app: dify-sandbox
  ports:
  - port: 8194
    targetPort: 8194
