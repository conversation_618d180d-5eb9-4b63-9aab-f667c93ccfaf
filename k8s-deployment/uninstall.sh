#!/bin/bash

# Dify Kubernetes 卸载脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 确认卸载
confirm_uninstall() {
    echo "========================================"
    echo "    Dify Kubernetes 卸载脚本"
    echo "========================================"
    echo
    log_warning "此操作将删除 Dify 的所有组件和数据！"
    echo
    read -p "确定要卸载 Dify 吗？(输入 'yes' 确认): " -r
    echo
    if [[ ! $REPLY == "yes" ]]; then
        log_info "取消卸载"
        exit 0
    fi
}

# 卸载 Dify
uninstall_dify() {
    log_info "开始卸载 Dify..."
    
    # 检查 namespace 是否存在
    if ! kubectl get namespace dify &> /dev/null; then
        log_warning "Dify namespace 不存在，可能已经被删除"
        return 0
    fi
    
    # 删除所有部署
    log_info "删除 Deployments..."
    kubectl delete deployments --all -n dify --ignore-not-found=true
    
    # 删除所有服务
    log_info "删除 Services..."
    kubectl delete services --all -n dify --ignore-not-found=true
    
    # 删除所有 ConfigMaps
    log_info "删除 ConfigMaps..."
    kubectl delete configmaps --all -n dify --ignore-not-found=true
    
    # 删除所有 PVC（这将删除所有数据）
    log_warning "删除 PersistentVolumeClaims（这将删除所有数据）..."
    kubectl delete pvc --all -n dify --ignore-not-found=true
    
    # 等待资源清理
    log_info "等待资源清理..."
    sleep 10
    
    # 删除 namespace
    log_info "删除 Namespace..."
    kubectl delete namespace dify --ignore-not-found=true
    
    # 等待 namespace 完全删除
    log_info "等待 Namespace 完全删除..."
    while kubectl get namespace dify &> /dev/null; do
        echo -n "."
        sleep 2
    done
    echo
    
    log_success "Dify 卸载完成"
}

# 清理残留资源
cleanup_resources() {
    log_info "检查残留资源..."
    
    # 检查是否有残留的 PV
    local dify_pvs=$(kubectl get pv -o jsonpath='{.items[?(@.spec.claimRef.namespace=="dify")].metadata.name}' 2>/dev/null || true)
    
    if [ -n "$dify_pvs" ]; then
        log_warning "发现残留的 PersistentVolumes:"
        echo "$dify_pvs"
        echo
        read -p "是否删除这些 PV？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            for pv in $dify_pvs; do
                log_info "删除 PV: $pv"
                kubectl delete pv "$pv" --ignore-not-found=true
            done
        fi
    fi
    
    log_success "资源清理完成"
}

# 主函数
main() {
    confirm_uninstall
    uninstall_dify
    cleanup_resources
    
    echo
    echo "========================================"
    echo "           卸载完成"
    echo "========================================"
    echo
    log_success "Dify 已完全卸载"
    log_info "如果需要重新部署，请运行 deploy.sh"
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
