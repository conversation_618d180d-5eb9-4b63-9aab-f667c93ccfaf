apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: weaviate-pvc
  namespace: dify
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-weaviate
  namespace: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dify-weaviate
  template:
    metadata:
      labels:
        app: dify-weaviate
    spec:
      containers:
      - name: weaviate
        image: semitechnologies/weaviate:1.19.0
        env:
        - name: PERSISTENCE_DATA_PATH
          value: "/var/lib/weaviate"
        - name: QUERY_DEFAULTS_LIMIT
          value: "25"
        - name: AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED
          value: "false"
        - name: DEFAULT_VECTORIZER_MODULE
          value: "none"
        - name: CLUSTER_HOSTNAME
          value: "node1"
        - name: AUTHENTICATION_APIKEY_ENABLED
          value: "true"
        - name: AUTHENTICATION_APIKEY_ALLOWED_KEYS
          value: "WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih"
        - name: AUTHENTICATION_APIKEY_USERS
          value: "<EMAIL>"
        - name: AUTHORIZATION_ADMINLIST_ENABLED
          value: "true"
        - name: AUTHORIZATION_ADMINLIST_USERS
          value: "<EMAIL>"
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: weaviate-storage
          mountPath: /var/lib/weaviate
        livenessProbe:
          httpGet:
            path: /v1/.well-known/live
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /v1/.well-known/ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: weaviate-storage
        persistentVolumeClaim:
          claimName: weaviate-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: dify-weaviate
  namespace: dify
spec:
  selector:
    app: dify-weaviate
  ports:
  - port: 8080
    targetPort: 8080
