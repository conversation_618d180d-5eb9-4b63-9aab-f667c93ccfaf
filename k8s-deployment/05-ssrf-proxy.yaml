apiVersion: v1
kind: ConfigMap
metadata:
  name: ssrf-proxy-config
  namespace: dify
data:
  squid.conf: |
    http_port 3128
    coredump_dir /var/spool/squid
    
    # Allow all HTTP methods
    acl Safe_ports port 80
    acl Safe_ports port 443
    acl Safe_ports port 8194
    acl CONNECT method CONNECT
    
    # Allow localhost and internal networks
    acl localnet src 10.0.0.0/8
    acl localnet src **********/12
    acl localnet src ***********/16
    acl localnet src fc00::/7
    acl localnet src fe80::/10
    
    # Allow access from local networks
    http_access allow localnet
    http_access allow localhost
    
    # Deny all other access
    http_access deny all
    
    # Reverse proxy for sandbox
    http_port 8194 accel defaultsite=sandbox vhost
    cache_peer sandbox parent 8194 0 no-query originserver name=sandbox
    acl sandbox_domain dstdomain sandbox
    cache_peer_access sandbox allow sandbox_domain
    
    # Basic settings
    cache_mem 256 MB
    maximum_object_size_in_memory 512 KB
    cache_dir ufs /var/spool/squid 1000 16 256
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-ssrf-proxy
  namespace: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dify-ssrf-proxy
  template:
    metadata:
      labels:
        app: dify-ssrf-proxy
    spec:
      containers:
      - name: ssrf-proxy
        image: ubuntu/squid:latest
        ports:
        - containerPort: 3128
        - containerPort: 8194
        volumeMounts:
        - name: squid-config
          mountPath: /etc/squid/squid.conf
          subPath: squid.conf
        env:
        - name: HTTP_PORT
          value: "3128"
        - name: COREDUMP_DIR
          value: "/var/spool/squid"
        - name: REVERSE_PROXY_PORT
          value: "8194"
        - name: SANDBOX_HOST
          value: "dify-sandbox"
        - name: SANDBOX_PORT
          value: "8194"
      volumes:
      - name: squid-config
        configMap:
          name: ssrf-proxy-config
---
apiVersion: v1
kind: Service
metadata:
  name: dify-ssrf-proxy
  namespace: dify
spec:
  selector:
    app: dify-ssrf-proxy
  ports:
  - name: proxy
    port: 3128
    targetPort: 3128
  - name: reverse-proxy
    port: 8194
    targetPort: 8194
