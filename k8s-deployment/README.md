# Dify Kubernetes 内网部署指南

本指南提供了在内网无外网连接环境下部署 Dify 到 Kubernetes 集群的完整方案。

## 目录结构

```
k8s-deployment/
├── 00-namespace.yaml          # Namespace 配置
├── 01-configmap.yaml          # 配置文件
├── 02-postgres.yaml           # PostgreSQL 数据库
├── 03-redis.yaml              # Redis 缓存
├── 04-weaviate.yaml           # Weaviate 向量数据库
├── 05-ssrf-proxy.yaml         # SSRF 代理服务
├── 06-sandbox.yaml            # 代码执行沙箱
├── 07-plugin-daemon.yaml      # 插件守护进程
├── 08-api.yaml                # Dify API 服务
├── 09-worker.yaml             # Dify Worker 服务
├── 10-web.yaml                # Dify Web 前端
├── 11-nginx.yaml              # Nginx 反向代理
├── deploy.sh                  # 部署脚本
├── uninstall.sh               # 卸载脚本
└── README.md                  # 本文档
```

## 前置要求

### 1. Kubernetes 集群
- Kubernetes 版本 >= 1.20
- 至少 3 个工作节点
- 每个节点至少 4GB 内存，2 CPU 核心
- 支持 PersistentVolume 的存储类

### 2. 镜像准备
在有网络的环境中准备以下镜像：

#### 自动化脚本（推荐）

**Linux/macOS:**
```bash
chmod +x export-images.sh
./export-images.sh
```

**Windows:**
```cmd
export-images.bat
```

#### 手动操作
```bash
# 拉取所有必需镜像
docker pull langgenius/dify-api:1.8.0
docker pull langgenius/dify-web:1.8.0
docker pull langgenius/dify-sandbox:0.2.12
docker pull langgenius/dify-plugin-daemon:0.2.0-local
docker pull postgres:15-alpine
docker pull redis:6-alpine
docker pull nginx:latest
docker pull semitechnologies/weaviate:1.19.0
docker pull ubuntu/squid:latest

# 导出镜像
docker save \
  langgenius/dify-api:1.8.0 \
  langgenius/dify-web:1.8.0 \
  langgenius/dify-sandbox:0.2.12 \
  langgenius/dify-plugin-daemon:0.2.0-local \
  postgres:15-alpine \
  redis:6-alpine \
  nginx:latest \
  semitechnologies/weaviate:1.19.0 \
  ubuntu/squid:latest \
  -o dify-images.tar
```

### 3. 镜像导入
将镜像文件传输到内网环境，并在所有 Kubernetes 节点上导入：

#### 使用导入脚本（推荐）

**Linux/macOS:**
```bash
chmod +x import-images.sh
./import-images.sh
```

**Windows:**
```cmd
import-images.bat
```

#### 手动导入
```bash
# 在每个节点上执行
docker load -i dify-images.tar

# 或者使用 containerd
ctr -n k8s.io images import dify-images.tar
```

## 部署步骤

### 1. 准备部署文件
```bash
# 下载或复制部署文件到目标环境
git clone <repository> # 或手动复制文件
cd k8s-deployment
```

### 2. 配置修改（可选）
根据实际环境修改 `01-configmap.yaml` 中的配置：

- 数据库密码
- Redis 密码
- 存储配置
- 资源限制等

### 3. 执行部署

#### Linux/macOS:
```bash
# 给脚本执行权限
chmod +x deploy.sh uninstall.sh

# 执行部署
./deploy.sh
```

#### Windows:
```cmd
# 执行部署
deploy.bat
```

### 4. 验证部署
```bash
# 查看所有 Pod 状态
kubectl get pods -n dify

# 查看服务状态
kubectl get services -n dify

# 查看 PVC 状态
kubectl get pvc -n dify
```

## 访问 Dify

部署完成后，可以通过以下方式访问：

### 1. LoadBalancer（推荐）
如果集群支持 LoadBalancer：
```bash
kubectl get service dify-nginx -n dify
```

### 2. NodePort
修改 `11-nginx.yaml` 中的服务类型为 NodePort：
```yaml
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080  # 可选，指定端口
```

### 3. Ingress
创建 Ingress 资源（需要 Ingress Controller）：
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dify-ingress
  namespace: dify
spec:
  rules:
  - host: dify.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dify-nginx
            port:
              number: 80
```

## 配置说明

### 存储配置
默认使用本地存储，生产环境建议使用：
- NFS
- Ceph RBD
- 云存储（如果可用）

### 资源配置
根据实际负载调整资源限制：
```yaml
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

### 高可用配置
- API 服务：默认 2 个副本
- Worker 服务：默认 2 个副本
- Web 服务：默认 2 个副本
- 数据库：单实例（可配置主从）

## 故障排除

### 1. Pod 启动失败
```bash
# 查看 Pod 详情
kubectl describe pod <pod-name> -n dify

# 查看 Pod 日志
kubectl logs <pod-name> -n dify
```

### 2. 镜像拉取失败
确保所有节点都已导入镜像：
```bash
# 检查节点镜像
docker images | grep dify
```

### 3. 存储问题
```bash
# 查看 PVC 状态
kubectl get pvc -n dify

# 查看 PV 状态
kubectl get pv
```

### 4. 网络问题
```bash
# 测试服务连通性
kubectl exec -it <pod-name> -n dify -- nslookup dify-postgres
```

## 维护操作

### 升级
1. 准备新版本镜像
2. 更新 YAML 文件中的镜像版本
3. 执行滚动更新：
```bash
kubectl set image deployment/dify-api api=langgenius/dify-api:new-version -n dify
```

### 备份
```bash
# 备份数据库
kubectl exec -it <postgres-pod> -n dify -- pg_dump -U postgres dify > dify-backup.sql

# 备份配置
kubectl get configmap dify-config -n dify -o yaml > dify-config-backup.yaml
```

### 卸载

**Linux/macOS:**
```bash
./uninstall.sh
```

**Windows:**
```cmd
uninstall.bat
```

## 安全建议

1. **网络策略**：配置 NetworkPolicy 限制 Pod 间通信
2. **RBAC**：创建专用的 ServiceAccount 和权限
3. **密钥管理**：使用 Secret 存储敏感信息
4. **镜像安全**：定期更新镜像，扫描漏洞

## 监控和日志

### 监控
推荐使用 Prometheus + Grafana：
```bash
# 添加监控标签
kubectl label namespace dify monitoring=enabled
```

### 日志
配置日志收集（如 ELK Stack）：
```yaml
# 在 Pod 中添加日志收集 sidecar
- name: filebeat
  image: elastic/filebeat:7.15.0
```

## 支持

如遇问题，请检查：
1. Kubernetes 集群状态
2. 镜像是否正确导入
3. 存储是否正常工作
4. 网络连通性

更多信息请参考 [Dify 官方文档](https://docs.dify.ai/)。
