@echo off
REM Dify 镜像导出脚本 - Windows 版本
REM 在有网络的环境中运行此脚本来准备所有必需的镜像

setlocal enabledelayedexpansion

echo ========================================
echo     Dify 镜像导出脚本
echo ========================================
echo.

REM 检查 Docker 是否可用
echo [INFO] 检查 Docker...
docker version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未找到，请先安装 Docker
    pause
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 无法连接到 Docker daemon，请检查 Docker 服务
    pause
    exit /b 1
)

echo [SUCCESS] Docker 连接正常

REM Dify 核心镜像列表
set images=langgenius/dify-api:1.8.0 langgenius/dify-web:1.8.0 langgenius/dify-sandbox:0.2.12 langgenius/dify-plugin-daemon:0.2.0-local postgres:15-alpine redis:6-alpine nginx:latest semitechnologies/weaviate:1.19.0 ubuntu/squid:latest

REM 拉取镜像
echo.
echo [INFO] 开始拉取 Dify 相关镜像...
echo.

set failed_count=0

for %%i in (%images%) do (
    echo [INFO] 拉取镜像: %%i
    docker pull "%%i"
    if errorlevel 1 (
        echo [ERROR] 拉取失败: %%i
        set /a failed_count+=1
    ) else (
        echo [SUCCESS] 成功拉取: %%i
    )
    echo.
)

if !failed_count! gtr 0 (
    echo [ERROR] 有 !failed_count! 个镜像拉取失败
    pause
    exit /b 1
)

echo [SUCCESS] 所有镜像拉取完成

REM 导出镜像
echo.
echo [INFO] 导出镜像到 dify-images.tar...

docker save %images% -o dify-images.tar

if errorlevel 1 (
    echo [ERROR] 镜像导出失败
    pause
    exit /b 1
)

echo [SUCCESS] 镜像导出完成: dify-images.tar

REM 显示文件大小
for %%A in (dify-images.tar) do (
    set size=%%~zA
    set /a size_mb=!size!/1024/1024
    echo [INFO] 文件大小: !size_mb! MB
)

REM 验证导出的镜像
echo.
echo [INFO] 验证导出的镜像...

if not exist "dify-images.tar" (
    echo [ERROR] 导出文件不存在
    pause
    exit /b 1
)

for %%A in (dify-images.tar) do (
    if %%~zA==0 (
        echo [ERROR] 导出文件为空
        pause
        exit /b 1
    )
)

echo [SUCCESS] 镜像文件验证通过

REM 生成导入脚本
echo.
echo [INFO] 生成镜像导入脚本...

echo @echo off > import-images.bat
echo REM Dify 镜像导入脚本 - Windows 版本 >> import-images.bat
echo. >> import-images.bat
echo echo [INFO] 检查镜像文件... >> import-images.bat
echo if not exist "dify-images.tar" ^( >> import-images.bat
echo     echo [ERROR] 镜像文件 dify-images.tar 不存在 >> import-images.bat
echo     pause >> import-images.bat
echo     exit /b 1 >> import-images.bat
echo ^) >> import-images.bat
echo. >> import-images.bat
echo echo [INFO] 开始导入 Dify 镜像... >> import-images.bat
echo docker load -i dify-images.tar >> import-images.bat
echo. >> import-images.bat
echo if errorlevel 1 ^( >> import-images.bat
echo     echo [ERROR] 镜像导入失败 >> import-images.bat
echo     pause >> import-images.bat
echo     exit /b 1 >> import-images.bat
echo ^) >> import-images.bat
echo. >> import-images.bat
echo echo [SUCCESS] 镜像导入完成 >> import-images.bat
echo. >> import-images.bat
echo echo [INFO] 已导入的镜像： >> import-images.bat
echo docker images ^| findstr /R "langgenius postgres redis nginx weaviate ubuntu" >> import-images.bat
echo. >> import-images.bat
echo echo [SUCCESS] 所有镜像已成功导入 >> import-images.bat
echo pause >> import-images.bat

echo [SUCCESS] Windows 导入脚本已生成: import-images.bat

REM 生成使用说明
echo.
echo [INFO] 生成使用说明...

echo # Dify 镜像导出包使用说明 > EXPORT_README.md
echo. >> EXPORT_README.md
echo ## 文件说明 >> EXPORT_README.md
echo. >> EXPORT_README.md
echo - `dify-images.tar` - Dify 所有必需的 Docker 镜像 >> EXPORT_README.md
echo - `import-images.bat` - Windows 镜像导入脚本 >> EXPORT_README.md
echo - `EXPORT_README.md` - 本说明文件 >> EXPORT_README.md
echo. >> EXPORT_README.md
echo ## 使用步骤 >> EXPORT_README.md
echo. >> EXPORT_README.md
echo ### 1. 传输文件 >> EXPORT_README.md
echo 将以下文件传输到内网环境： >> EXPORT_README.md
echo - dify-images.tar >> EXPORT_README.md
echo - import-images.bat >> EXPORT_README.md
echo. >> EXPORT_README.md
echo ### 2. 导入镜像 >> EXPORT_README.md
echo. >> EXPORT_README.md
echo 双击运行 `import-images.bat` 或在命令行中执行： >> EXPORT_README.md
echo ```cmd >> EXPORT_README.md
echo import-images.bat >> EXPORT_README.md
echo ``` >> EXPORT_README.md
echo. >> EXPORT_README.md
echo ### 3. 验证导入 >> EXPORT_README.md
echo ```cmd >> EXPORT_README.md
echo docker images ^| findstr "langgenius postgres redis nginx weaviate ubuntu" >> EXPORT_README.md
echo ``` >> EXPORT_README.md
echo. >> EXPORT_README.md
echo 更多信息请参考完整的部署文档。 >> EXPORT_README.md

echo [SUCCESS] 使用说明已生成: EXPORT_README.md

echo.
echo ========================================
echo            导出完成
echo ========================================
echo.
echo [SUCCESS] 镜像导出包已准备完成！
echo.
echo 生成的文件：
echo   - dify-images.tar ^(镜像文件^)
echo   - import-images.bat ^(Windows 导入脚本^)
echo   - EXPORT_README.md ^(使用说明^)
echo.
echo 请将这些文件传输到内网环境并按照说明进行导入。
echo.

pause
