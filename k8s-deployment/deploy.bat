@echo off
REM Dify Kubernetes 部署脚本 - Windows 版本
REM 适用于无外网连接的 Kubernetes 集群

setlocal enabledelayedexpansion

echo ========================================
echo     Dify Kubernetes 内网部署脚本
echo ========================================
echo.

REM 检查 kubectl 是否可用
echo [INFO] 检查 kubectl...
kubectl version --client >nul 2>&1
if errorlevel 1 (
    echo [ERROR] kubectl 未找到，请先安装 kubectl
    pause
    exit /b 1
)

kubectl cluster-info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 无法连接到 Kubernetes 集群，请检查 kubeconfig
    pause
    exit /b 1
)

echo [SUCCESS] Kubernetes 集群连接正常

REM 部署 Dify
echo.
echo [INFO] 开始部署 Dify...

REM 按顺序部署各个组件
set yaml_files=00-namespace.yaml 01-configmap.yaml 02-postgres.yaml 03-redis.yaml 04-weaviate.yaml 05-ssrf-proxy.yaml 06-sandbox.yaml 07-plugin-daemon.yaml 08-api.yaml 09-worker.yaml 10-web.yaml 11-nginx.yaml

for %%f in (%yaml_files%) do (
    if exist "%%f" (
        echo [INFO] 部署 %%f...
        kubectl apply -f "%%f"
        
        REM 等待关键服务启动
        if "%%f"=="02-postgres.yaml" (
            echo [INFO] 等待 PostgreSQL 启动...
            kubectl wait --for=condition=ready pod -l app=dify-postgres -n dify --timeout=300s
        )
        if "%%f"=="03-redis.yaml" (
            echo [INFO] 等待 Redis 启动...
            kubectl wait --for=condition=ready pod -l app=dify-redis -n dify --timeout=300s
        )
        if "%%f"=="04-weaviate.yaml" (
            echo [INFO] 等待 Weaviate 启动...
            kubectl wait --for=condition=ready pod -l app=dify-weaviate -n dify --timeout=300s
        )
    ) else (
        echo [ERROR] 文件 %%f 不存在
        pause
        exit /b 1
    )
)

echo [SUCCESS] Dify 部署完成

REM 等待所有服务启动
echo.
echo [INFO] 等待所有服务启动...
timeout /t 30 /nobreak >nul

REM 检查部署状态
echo.
echo [INFO] 检查部署状态...
echo.
echo === Namespace ===
kubectl get namespace dify

echo.
echo === Pods ===
kubectl get pods -n dify -o wide

echo.
echo === Services ===
kubectl get services -n dify

echo.
echo === PersistentVolumeClaims ===
kubectl get pvc -n dify

echo.
echo ========================================
echo            部署完成
echo ========================================
echo.

REM 获取访问地址
for /f "tokens=*" %%i in ('kubectl get service dify-nginx -n dify -o jsonpath^="{.status.loadBalancer.ingress[0].ip}" 2^>nul') do set nginx_ip=%%i
if "!nginx_ip!"=="" (
    for /f "tokens=*" %%i in ('kubectl get service dify-nginx -n dify -o jsonpath^="{.spec.clusterIP}"') do set nginx_ip=%%i
)

echo [SUCCESS] Dify 部署成功！
echo 访问地址: http://!nginx_ip!
echo.
echo 如果使用 LoadBalancer 类型的服务，请等待外部 IP 分配完成
echo 可以使用以下命令查看服务状态：
echo   kubectl get service dify-nginx -n dify -w
echo.

pause
