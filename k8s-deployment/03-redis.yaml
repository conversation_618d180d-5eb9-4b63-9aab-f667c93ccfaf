apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: dify
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dify-redis
  namespace: dify
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dify-redis
  template:
    metadata:
      labels:
        app: dify-redis
    spec:
      containers:
      - name: redis
        image: redis:6-alpine
        command:
        - redis-server
        - --requirepass
        - difyai123456
        env:
        - name: REDISCLI_AUTH
          valueFrom:
            configMapKeyRef:
              name: dify-config
              key: REDIS_PASSWORD
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - difyai123456
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - difyai123456
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: dify-redis
  namespace: dify
spec:
  selector:
    app: dify-redis
  ports:
  - port: 6379
    targetPort: 6379
