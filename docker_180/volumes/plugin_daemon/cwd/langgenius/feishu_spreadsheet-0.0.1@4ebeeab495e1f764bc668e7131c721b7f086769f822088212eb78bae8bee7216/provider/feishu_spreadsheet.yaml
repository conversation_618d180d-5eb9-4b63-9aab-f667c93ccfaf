credentials_for_provider:
  app_id:
    help:
      en_US: Get your app_id and app_secret from <PERSON><PERSON><PERSON>
      <PERSON>h_<PERSON>: 从飞书获取您的 app_id 和 app_secret
    label:
      en_US: APP ID
    placeholder:
      en_US: Please input your feishu app id
      zh_Hans: 请输入你的飞书 app id
    required: true
    type: text-input
    url: https://open.larkoffice.com/app
  app_secret:
    label:
      en_US: APP Secret
    placeholder:
      en_US: Please input your app secret
      zh_Hans: 请输入你的飞书 app secret
    required: true
    type: secret-input
extra:
  python:
    source: provider/feishu_spreadsheet.py
identity:
  author: <PERSON> Lea
  description:
    en_US: 'Feishu Spreadsheet, requires the following permissions: sheets:spreadsheet.

      '
    zh_Hans: '飞书电子表格，需要开通以下权限: sheets:spreadsheet。

      '
  icon: icon.png
  label:
    en_US: Feishu Spreadsheet
    zh_Hans: 飞书电子表格
  name: feishu_spreadsheet
  tags:
  - social
  - productivity
tools:
- tools/create_spreadsheet.yaml
- tools/add_rows.yaml
- tools/read_rows.yaml
- tools/read_table.yaml
- tools/get_spreadsheet.yaml
- tools/read_cols.yaml
- tools/add_cols.yaml
- tools/list_spreadsheet_sheets.yaml
