# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o ./requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==4.10.0
    # via
    #   httpx
    #   openai
blinker==1.9.0
    # via flask
certifi==2025.8.3
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via gevent
charset-normalizer==3.4.3
    # via requests
click==8.2.1
    # via flask
colorama==0.4.6
    # via
    #   click
    #   tqdm
dify-plugin==0.4.3
    # via openai-api-compatible-gnrzr6cey (pyproject.toml)
distro==1.9.0
    # via openai
dpkt==1.9.8
    # via dify-plugin
flask==3.0.3
    # via dify-plugin
gevent==25.5.1
    # via dify-plugin
greenlet==3.2.4
    # via gevent
h11==0.16.0
    # via httpcore
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   dify-plugin
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
itsdangerous==2.2.0
    # via flask
jinja2==3.1.6
    # via flask
jiter==0.10.0
    # via openai
markupsafe==3.0.2
    # via
    #   jinja2
    #   werkzeug
multidict==6.6.4
    # via yarl
openai==1.99.9
    # via openai-api-compatible-gnrzr6cey (pyproject.toml)
packaging==25.0
    # via dify-plugin
propcache==0.3.2
    # via yarl
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   dify-plugin
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via dify-plugin
python-dotenv==1.1.1
    # via pydantic-settings
pyyaml==6.0.2
    # via dify-plugin
regex==2025.7.34
    # via tiktoken
requests==2.32.4
    # via
    #   dify-plugin
    #   tiktoken
setuptools==80.9.0
    # via
    #   zope-event
    #   zope-interface
sniffio==1.3.1
    # via
    #   anyio
    #   openai
socksio==1.0.0
    # via dify-plugin
tiktoken==0.8.0
    # via dify-plugin
tqdm==4.67.1
    # via openai
typing-extensions==4.14.1
    # via
    #   anyio
    #   openai
    #   pydantic
    #   pydantic-core
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
urllib3==2.5.0
    # via requests
werkzeug==3.0.6
    # via
    #   dify-plugin
    #   flask
yarl==1.20.1
    # via dify-plugin
zope-event==5.1.1
    # via gevent
zope-interface==7.2
    # via gevent
