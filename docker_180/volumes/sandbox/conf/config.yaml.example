app:
  port: 8194
  debug: True
  key: dify-sandbox
max_workers: 4
max_requests: 50
worker_timeout: 5
python_path: /usr/local/bin/python3
python_lib_path:
  - /usr/local/lib/python3.10
  - /usr/lib/python3.10
  - /usr/lib/python3
  - /usr/lib/x86_64-linux-gnu
  - /etc/ssl/certs/ca-certificates.crt
  - /etc/nsswitch.conf
  - /etc/hosts
  - /etc/resolv.conf
  - /run/systemd/resolve/stub-resolv.conf
  - /run/resolvconf/resolv.conf
  - /etc/localtime
  - /usr/share/zoneinfo
  - /etc/timezone
  # add more paths if needed
python_pip_mirror_url: https://pypi.tuna.tsinghua.edu.cn/simple
nodejs_path: /usr/local/bin/node
enable_network: True
allowed_syscalls:
  - 1
  - 2
  - 3
  # add all the syscalls which you require
proxy:
  socks5: ''
  http: ''
  https: ''
